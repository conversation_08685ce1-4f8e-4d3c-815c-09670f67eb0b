"use client";

import CustomToast from "@/Components/Common/CustomToast";
import { useRouter } from "nextjs-toploader/app";
import { useCallback, useState } from "react";

type ApiFunction<P = unknown, R = unknown> = (payload: P) => Promise<R>;
type ResponseHandler<R = unknown> = (response: R) => void;
type ErrorHandler = (error: Error | ApiResponse) => void;

interface ApiResponse {
  status?: number | string;
  message?: string;
  response?: { status?: number };
  [key: string]: unknown;
}

interface UseApiRequestReturn {
  isLoading: boolean;
  sendRequest: <P>(
    functionCall: ApiFunction<P, ApiResponse>,
    responseHandler?: ResponseHandler<ApiResponse>,
    payload?: P,
    successMessage?: string,
    errorHandler?: ErrorHandler | null
  ) => Promise<void>;
}

const useApiRequest = (defaultLoading: boolean = true): UseApiRequestReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(defaultLoading);
  const router = useRouter();

  const sendRequest = useCallback(
    async <P>(
      functionCall: ApiFunction<P, ApiResponse>,
      responseHandler?: ResponseHandler<ApiResponse>,
      payload?: P,
      successMessage: string = "",
      errorHandler: ErrorHandler | null = null
    ) => {
      setIsLoading(true);

      try {
        const data = await functionCall(payload as P);

        if (data?.status === 200 || data?.status === "success") {
          responseHandler?.(data);
          if (successMessage) CustomToast.success(successMessage);
        } else {
          throw data;
        }
      } catch (error: unknown) {
        // console.error(error);
        setIsLoading(false);

        const isApiError = (err: unknown): err is ApiResponse =>
          typeof err === "object" && err !== null;

        if (errorHandler) {
          if (isApiError(error) || error instanceof Error) {
            errorHandler(error as Error | ApiResponse);
          }
        }

        // if (isApiError(error)) {
        //   if (error.status === 401) {
        //     CustomToast.error(error.message || "Access denied");
        //     router.push("/");
        //     return;
        //   }
        // }
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  return { isLoading, sendRequest };
};

export default useApiRequest;
