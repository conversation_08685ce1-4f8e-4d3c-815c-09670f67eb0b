import { C<PERSON>, <PERSON> } from "lucide-react";
import Link from "next/link";

const HeroSection = () => {
  return (
    <>
      <section className="relative gradient-bg py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Master Electronics
              <span className="gradient-text block">Bit by Bit</span>
            </h1>
            <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto">
              Your comprehensive platform for semiconductor interview
              preparation. Practice problems, join contests, and excel in your
              electronics career.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/practice"
                className="btn-primary text-lg px-8 py-3 flex items-center justify-center space-x-2"
              >
                <Cpu className="w-5 h-5" />
                <span>Start Practicing</span>
              </Link>
              <Link
                href="/login"
                className="btn-accent text-lg px-8 py-3 flex items-center justify-center space-x-2"
              >
                <Trophy className="w-5 h-5" />
                <span>Join Contest</span>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default HeroSection;
