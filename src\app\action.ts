/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";

import { buildQuery, TOKEN } from "@/utils/function";
import axios, { AxiosError } from "axios";
import { cookies } from "next/headers";
const Services = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BASE_URL,
  headers: {
    Accept: "application/json",
    // Authorization: `Bearer ${authStorage?.getAuthToken() ? authStorage?.getAuthToken() : ""}`,
    // 'X-RapidAPI-Key': `${NEXT_API_KEY}`,
  },
});

Services.interceptors.request.use(
  async (config) => {
    const cookieStore = await cookies();
    const token = cookieStore.get(TOKEN)?.value;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

const apiType = {
  get: "GET",
  post: "POST",
  patch: "PATCH",
  delete: "DELETE",
};

export const sendAPIRequest = async (
  api: {
    endpoint: string;
    type: string;
  },
  payload?: any
) => {
  try {
    let response;
    switch (api.type) {
      case "POST":
        response = await Services.post(api.endpoint, payload);
        break;

      case "PUT":
        response = await Services.put(api.endpoint, payload);

        break;
      case "DELETE":
        response = await Services.delete(api.endpoint);
        break;

      case "PATCH":
        response = await Services.patch(api.endpoint, payload);
        break;

      default:
        const queryParams = buildQuery(payload);
        // const queryParams = URLSearchParams(payload)?.toString();

        // console.dir(queryParams, "query", api)

        response = await Services.get(api.endpoint + queryParams);
        break;
    }

    return response?.data;
  } catch (error) {
    // console.log(error, "error");
    if (error instanceof AxiosError) {
      return error?.response?.data;
      // return error?.response?.data;
    }
    return error;
  }
};

// ---------------> AUTH API <-------------------
// Login
export const loginAPI = async (payload: {
  email: string;
  password: string;
}) => {
  const login = {
    endpoint: "/auth/login",
    type: apiType.post,
  };
  try {
    const response = await sendAPIRequest(login, payload);
    return response;
  } catch (error) {
    return error;
  }
};
// Social Login
export const socialLoginAPI = async (payload: { firebaseToken: string }) => {
  const login = {
    endpoint: "/auth/social-auth",
    type: apiType.post,
  };
  try {
    const response = await sendAPIRequest(login, payload);
    return response;
  } catch (error) {
    return error;
  }
};
// Sign Up API
export const signUpAPI = async (payload: {
  name: string;
  email: string;
  password: string;
}) => {
  const singUp = {
    endpoint: "/auth/register",
    type: apiType.post,
  };
  try {
    const response = await sendAPIRequest(singUp, payload);
    return response;
  } catch (error) {
    return error;
  }
};
// Get Me API
export const getMeAPI = async () => {
  const getMe = {
    endpoint: "/auth/get-me",
    type: apiType.get,
  };
  try {
    const response = await sendAPIRequest(getMe);
    return response;
  } catch (error) {
    return error;
  }
};
// Update Me API
export const updateMeAPI = async (payload: { password: string }) => {
  const updateMe = {
    endpoint: "/auth/update-me",
    type: apiType.patch,
  };
  try {
    const response = await sendAPIRequest(updateMe, payload);
    return response;
  } catch (error) {
    return error;
  }
};

export const forgotPasswordAPI = async (payload: { email: string }) => {
  const forgotPassword = {
    endpoint: "/auth/forgot-password",
    type: apiType.post,
  };
  try {
    const response = await sendAPIRequest(forgotPassword, payload);
    return response;
  } catch (error) {
    return error;
  }
};
export const verifyOtpAPI = async (payload: { otp: string }) => {
  const verifyOTP = {
    endpoint: "/auth/verify-otp-for-password",
    type: apiType.post,
  };
  try {
    const response = await sendAPIRequest(verifyOTP, payload);
    return response;
  } catch (error) {
    return error;
  }
};
