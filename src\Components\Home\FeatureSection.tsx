import { <PERSON><PERSON>, Trophy, BookOpen, Users, Target, Award } from "lucide-react";
const FeatureSection = () => {
  const features = [
    {
      icon: Cpu,
      title: "Practice Problems",
      description:
        "Access a vast collection of semiconductor and electronics problems with detailed solutions.",
    },
    {
      icon: Trophy,
      title: "Contest Mode",
      description:
        "Compete with other students in timed contests to test your knowledge.",
    },
    {
      icon: BookOpen,
      title: "Learning Resources",
      description:
        "Comprehensive study materials covering all aspects of electronics and semiconductor theory.",
    },
    {
      icon: Users,
      title: "Community",
      description:
        "Connect with fellow electronics enthusiasts and share knowledge.",
    },
    {
      icon: Target,
      title: "Track Progress",
      description:
        "Monitor your learning progress with detailed analytics and performance metrics.",
    },
    {
      icon: Award,
      title: "Certifications",
      description:
        "Earn certificates upon completing different modules and contests.",
    },
  ];
  return (
    <>
      <section className="py-20 bg-slate-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Why Choose ElectronicBit?
            </h2>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              Comprehensive tools and resources designed specifically for
              electronics and semiconductor interview preparation.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="card card-hover">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mr-4 shadow-lg">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-white">
                      {feature.title}
                    </h3>
                  </div>
                  <p className="text-slate-300">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>
    </>
  );
};

export default FeatureSection;
