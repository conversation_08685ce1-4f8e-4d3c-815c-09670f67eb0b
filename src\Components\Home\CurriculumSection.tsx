"use client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Rocket, Handshake } from "lucide-react";

const curriculumDifference = [
  {
    title: "Industry-First Approach",
    description:
      "Learn the same methodologies used at Google, NVIDIA, and other tech giants. No outdated academic theory.",
    color: "from-green-600 to-teal-600",
    icon: BrainCircuit,
  },
  {
    title: "Real Project Experience",
    description:
      "Work on actual semiconductor design challenges, not toy problems. Build a portfolio that impresses.",
    color: "from-blue-600 to-purple-600",
    icon: Rocket,
  },
  {
    title: "Direct Industry Connections",
    description:
      "Leverage our network of engineers at top companies for mentorship and job referrals.",
    color: "from-purple-600 to-pink-600",
    icon: Handshake,
  },
];

export default function CurriculumSection() {
  return (
    <section className="py-20 bg-slate-800/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            What Makes Our Curriculum Different?
          </h2>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            Real industry insights from someone who&apos;s been in the trenches
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {curriculumDifference.map((item, index) => (
            <div key={index} className="card card-hover">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-6">
                <item.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">
                {item.title}
              </h3>
              <p className="text-gray-200 mb-6">{item.description}</p>
              <div className="bg-green-500 text-white text-xs font-bold px-3 py-1 rounded-full inline-block">
                {item.title}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
