"use client";

import { User } from "@/type/Header";
import React, { createContext, useContext, useState, ReactNode } from "react";

type GlobalContextType = {
  isLogin: boolean;
  setIsLogin: (value: boolean) => void;
  userData: User | null;
  setUserData: (value: User | null) => void;
};

const GlobalContext = createContext<GlobalContextType | undefined>(undefined);

export const GlobalContextProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [isLogin, setIsLogin] = useState(false); // default: not logged in
  const [userData, setUserData] = useState<User | null>(null);

  return (
    <GlobalContext.Provider
      value={{
        isLogin,
        setIsLogin,
        userData,
        setUserData,
      }}
    >
      {children}
    </GlobalContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(GlobalContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
