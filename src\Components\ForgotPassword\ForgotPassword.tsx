"use client";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "../ui/input-otp";
import useApiRequest from "@/utils/hook/useApiRequest";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import Link from "next/link";
import { useState } from "react";
import { setCookie } from "typescript-cookie";
import CustomToast from "../Common/CustomToast";
import { forgotPasswordAPI, verifyOtpAPI } from "@/app/action";
import { routes, TOKEN } from "@/utils/function";
import { useRouter } from "nextjs-toploader/app";

interface OtpFormValues {
  otp: string;
}

const ForgotPassword: React.FC = () => {
  const api = useApiRequest(false);
  const router = useRouter();

  // session storage check
  const [isEmailEntered, setIsEmailEntered] = useState(false);
  const [storedEmail, setStoredEmail] = useState<string | null>(null);

  // Email form schema
  const emailValidationSchema = Yup.object({
    email: Yup.string().email("Invalid email").required("Email is required"),
  });

  // OTP form schema
  const otpValidationSchema = Yup.object({
    otp: Yup.string()
      .length(6, "OTP must be exactly 6 digits")
      .required("OTP is required"),
  });

  // Email submit
  const onEmailSubmit = async (values: { email: string }) => {
    api.sendRequest(
      forgotPasswordAPI,
      (res: any) => {
        setCookie(TOKEN, res?.data?.token);
        CustomToast.success(res?.message);
        sessionStorage.clear();
        setStoredEmail(values.email);
        setIsEmailEntered(true);
      },
      { ...values },
      "",
      (error) => {
        // console.log("error", error);
        CustomToast.error(error?.message || "Something went wrong");
      }
    );
  };

  // OTP submit
  const onOtpSubmit = async (values: { otp: string }) => {
    // console.log("Verifying OTP", values.otp, "for", storedEmail);
    api.sendRequest(
      verifyOtpAPI,
      (res: any) => {
        CustomToast.success(res?.message);
        setCookie(TOKEN, res?.data?.token);
        router.push(routes.RESET_PASSWORD);
      },
      { otp: values.otp },
      "",
      (error) => {
        // console.log("error", error);
        console.log(error);
        CustomToast.error(error?.message || "Something went wrong");
      }
    );
    // Call verify OTP API
  };

  return (
    <section className="min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Show Email Form if not entered */}
        {!isEmailEntered ? (
          <>
            <div className="text-center">
              <h1 className="text-3xl font-bold text-white mb-2">
                Enter Registered Email
              </h1>
              <p className="text-gray-400">
                We’ll send you an OTP to reset your password
              </p>
            </div>
            <div className="card">
              <Formik
                initialValues={{ email: "" }}
                validationSchema={emailValidationSchema}
                onSubmit={onEmailSubmit}
              >
                {({ isSubmitting }) => (
                  <Form className="space-y-6">
                    <div>
                      <Field
                        type="email"
                        name="email"
                        placeholder="Enter your email"
                        className="w-full px-4 py-3 rounded-lg bg-gray-800 text-white border border-gray-700 focus:outline-none focus:border-primary"
                      />
                      <ErrorMessage
                        name="email"
                        component="div"
                        className="text-red-400 text-sm mt-1"
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting || api.isLoading}
                      className="w-full btn-primary py-3 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {api.isLoading ? "Sending OTP..." : "Send OTP"}
                    </button>
                  </Form>
                )}
              </Formik>
            </div>
          </>
        ) : (
          // OTP Form
          <>
            <div className="text-center">
              <h1 className="text-3xl font-bold text-white mb-2">Verify OTP</h1>
              <p className="text-gray-400">
                Enter the 6-digit OTP we sent to{" "}
                <span className="font-semibold text-primary">
                  {storedEmail}
                </span>
              </p>
            </div>
            <div className="card">
              <Formik<OtpFormValues>
                initialValues={{ otp: "" }}
                validationSchema={otpValidationSchema}
                onSubmit={onOtpSubmit}
              >
                {({ values, setFieldValue, isSubmitting }) => (
                  <Form className="space-y-6">
                    <div>
                      <InputOTP
                        autoFocus
                        maxLength={6}
                        value={values.otp ?? ""}
                        onChange={(value) => setFieldValue("otp", value)}
                        pattern={REGEXP_ONLY_DIGITS}
                      >
                        <InputOTPGroup className="mx-auto">
                          {[...Array(6)].map((_, i) => (
                            <InputOTPSlot
                              className="h-12 w-12 text-2xl font-medium"
                              key={i}
                              index={i}
                            />
                          ))}
                        </InputOTPGroup>
                      </InputOTP>
                      <ErrorMessage
                        name="otp"
                        component="div"
                        className="text-red-400 text-sm mt-1"
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting || api.isLoading}
                      className="w-full btn-primary py-3 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {api.isLoading ? "Verifying..." : "Verify OTP"}
                    </button>
                  </Form>
                )}
              </Formik>
            </div>
          </>
        )}

        {/* Already have account */}
        <div className="text-center mt-6">
          <p className="text-gray-400">
            Already have an account?{" "}
            <Link href="/login" className="text-primary ">
              Login
            </Link>
          </p>
        </div>
      </div>
    </section>
  );
};

export default ForgotPassword;
