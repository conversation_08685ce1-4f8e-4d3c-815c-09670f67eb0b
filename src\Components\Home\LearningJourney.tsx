const stages = [
  {
    title: "Digital Fundamentals",
    description: "Boolean algebra, logic gates, and basic digital concepts.",
    duration: "2 weeks",
    level: "Beginner",
    levelColor: "bg-green-500",
    icon: (
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
      />
    ),
    highlight: true,
  },
  {
    title: "Verilog Basics",
    description: "Learn hardware description language from scratch.",
    duration: "3 weeks",
    level: "Beginner",
    levelColor: "bg-green-500",
    icon: (
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
      />
    ),
  },
  {
    title: "RTL Design",
    description: "Register Transfer Level design and implementation.",
    duration: "4 weeks",
    level: "Intermediate",
    levelColor: "bg-yellow-500",
    icon: (
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
      />
    ),
  },
  {
    title: "Design for Test",
    description: "DFT concepts, SCAN, ATPG, and testability.",
    duration: "3 weeks",
    level: "Intermediate",
    levelColor: "bg-yellow-500",
    icon: (
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
      />
    ),
  },
  {
    title: "Interview Prep",
    description: "Mock interviews and company-specific preparation.",
    duration: "2 weeks",
    level: "Advanced",
    levelColor: "bg-red-500",
    icon: (
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6z"
      />
    ),
  },
];

export default function LearningJourney() {
  return (
    <section className="py-20 bg-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Your VLSI Learning Journey
          </h2>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            A structured curriculum designed specifically for freshers entering
            the semiconductor industry
          </p>
        </div>

        <div className="relative">
          {/* Vertical Timeline Line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-blue-600 to-purple-600 h-full"></div>

          <div className="space-y-12">
            {stages.map((stage, index) => {
              const isEven = index % 2 === 1;
              return (
                <div key={index} className="relative flex items-center">
                  {/* Icon */}
                  <div
                    className={`absolute left-1/2 transform -translate-x-1/2 w-16 h-16 rounded-full flex items-center justify-center shadow-lg border-4 border-slate-800 ${
                      stage.highlight
                        ? "bg-gradient-to-r from-blue-600 to-purple-600"
                        : "bg-slate-700"
                    }`}
                  >
                    <svg
                      className="w-8 h-8 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      {stage.icon}
                    </svg>
                  </div>

                  {/* Content Box */}
                  <div
                    className={`w-5/12 bg-slate-800 border border-slate-700 rounded-xl p-6 shadow-xl card-hover ${
                      isEven ? "mr-auto" : "ml-auto"
                    }`}
                  >
                    <h3 className="text-base md:text-xl font-bold text-white mb-2">
                      {stage.title}
                    </h3>
                    <p className="text-slate-300 mb-4 text-xs md:text-base">
                      {stage.description}
                    </p>
                    <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
                      <p className="text-slate-400 text-sm ">
                        Duration: {stage.duration}
                      </p>
                      <p
                        className={`${stage.levelColor} text-center text-white text-xs font-bold px-3 py-1 rounded-full`}
                      >
                        {stage.level}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
