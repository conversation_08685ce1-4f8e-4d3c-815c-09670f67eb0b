import { toast } from "sonner";

const CustomToast = {
  success: (message: string) => {
    toast.success(message, {
      style: {
        background: "#10BE5B", // Green background
        color: "white",
        border: "1px solid #10BE5B",
      },
    });
  },
  error: (message: string) => {
    toast.error(message, {
      style: {
        background: "#EF3B41", // Red background
        color: "white",
        border: "1px solid #EF3B41",
      },
    });
  },
  // Additional wrappers can be added here if needed
};

export default CustomToast;
