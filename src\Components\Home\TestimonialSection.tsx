import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import CustomImage from "../Common/CustomImage";

const TestimonialSection = () => {
  const [current, setCurrent] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      role: "VLSI Engineer @ Intel",
      avatar: "/avatars/ananya.jpg",
      color: "from-blue-600 to-purple-600",
      text: "ElectronicBit's practice problems and contests helped me ace my campus interviews. The explanations are clear and the platform is super intuitive!",
    },
    {
      name: "<PERSON><PERSON>",
      role: "Student @ IIT Bombay",
      avatar: "/avatars/rahul.jpg",
      color: "from-green-600 to-teal-600",
      text: "The structured learning path and real-world problems made my preparation much more effective. Highly recommended for anyone in electronics!",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Design Intern @ Qualcomm",
      avatar: "/avatars/sneha.jpg",
      color: "from-orange-500 to-yellow-500",
      text: "I loved the community and the detailed solutions. The platform feels modern and the analytics helped me track my progress.",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Verification Engineer @ AMD",
      avatar: "/avatars/vikram.jpg",
      color: "from-pink-500 to-red-500",
      text: "The best platform for semiconductor interview prep! The problems are challenging and the UI is fantastic.",
    },
  ];

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setCurrent((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, []);

  const goTo = (idx: number) => {
    setCurrent(idx);
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      setCurrent((prev) => (prev + 1) % testimonials.length);
    }, 5000);
  };

  const prev = () =>
    goTo((current - 1 + testimonials.length) % testimonials.length);
  const next = () => goTo((current + 1) % testimonials.length);

  return (
    <section className="py-20 bg-slate-800/70">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Heading */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            What do our subscribers say?
          </h2>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            Hear from learners and professionals who have used ElectronicBit to
            boost their electronics and semiconductor skills.
          </p>
        </div>

        {/* Carousel Wrapper */}
        <div className="relative flex items-center justify-center">
          {/* Left Arrow */}
          <button
            className="absolute left-0 z-10 p-2 rounded-full hover:bg-slate-700/60 transition"
            onClick={prev}
            aria-label="Previous testimonial"
          >
            <svg width="36" height="36" fill="none" viewBox="0 0 24 24">
              <path
                stroke="#fff"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>

          {/* Cards */}
          <div
            className="flex w-full justify-center items-center"
            style={{ minHeight: 320 }}
          >
            {testimonials.map((t, idx) => {
              // Current Card (always visible)
              if (idx === current) {
                return (
                  <div
                    key={idx}
                    className="bg-slate-900/80 border border-slate-700 rounded-xl p-8 shadow-2xl flex flex-col items-center mx-4 z-20 transition-all duration-500 w-full max-w-xl scale-105"
                  >
                    <div
                      className={`w-20 h-20 rounded-full bg-gradient-to-br ${t.color} flex items-center justify-center text-white font-bold text-3xl mb-4 overflow-hidden `}
                    >
                      {t.avatar ? (
                        <CustomImage
                          src={t.avatar}
                          alt={t.name}
                          width={80}
                          height={80}
                          className="object-cover rounded-full"
                        />
                      ) : (
                        t.name[0]
                      )}
                    </div>
                    <div className="text-xl font-semibold text-white mb-1">
                      {t.name}
                    </div>
                    <div className="text-slate-400 text-base mb-2">
                      {t.role}
                    </div>
                    <p className="text-slate-200 text-lg mb-2 text-center">
                      “{t.text}”
                    </p>
                  </div>
                );
              }

              // Blurred side cards only on md+
              if (
                idx ===
                  (current - 1 + testimonials.length) % testimonials.length ||
                idx === (current + 1) % testimonials.length
              ) {
                return (
                  <div
                    key={idx}
                    className="hidden md:flex bg-slate-900/60 border border-slate-700 rounded-xl p-8 shadow-lg flex-col items-center mx-4 z-10 transition-all duration-500 w-full max-w-md scale-95 blur-sm opacity-60"
                    style={{ pointerEvents: "none" }}
                  >
                    <div
                      className={`w-16 h-16 rounded-full bg-gradient-to-br ${t.color} flex items-center justify-center text-white font-bold text-2xl mb-4 overflow-hidden `}
                    >
                      {t.avatar ? (
                        <CustomImage
                          src={t.avatar}
                          alt={t.name}
                          width={64}
                          height={64}
                          className="object-cover rounded-full"
                        />
                      ) : (
                        t.name[0]
                      )}
                    </div>
                    <div className="text-lg font-semibold text-white mb-1">
                      {t.name}
                    </div>
                    <div className="text-slate-400 text-sm mb-2">{t.role}</div>
                    <p className="text-slate-200 text-base mb-2 text-center">
                      “{t.text}”
                    </p>
                  </div>
                );
              }

              return null;
            })}
          </div>

          {/* Right Arrow */}
          <button
            className="absolute right-0 z-10 p-2 rounded-full hover:bg-slate-700/60 transition"
            onClick={next}
            aria-label="Next testimonial"
          >
            <svg width="36" height="36" fill="none" viewBox="0 0 24 24">
              <path
                stroke="#fff"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>

        {/* Pagination */}
        <div className="flex justify-center mt-6 space-x-2">
          {testimonials.map((_, idx) => (
            <button
              key={idx}
              className={`w-3 h-3 rounded-full ${
                idx === current ? "bg-white" : "bg-slate-500"
              } transition-all`}
              onClick={() => goTo(idx)}
              aria-label={`Go to testimonial ${idx + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialSection;
