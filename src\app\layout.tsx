import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/Components/Header/Header";
import ProgressLoader from "@/Components/Common/ProgressLoader";
import { Toaster } from "sonner";
import { GlobalContextProvider } from "@/utils/Context/GlobalContext";
import Footer from "@/Components/Footer/Footer";

const interFont = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ElectronicBit Webapp",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${interFont.variable} ${interFont.variable} antialiased className=" bg-gray-900 text-gray-100"`}
      >
        <ProgressLoader />
        <GlobalContextProvider>
          <Header />
          <main className="">{children}</main>
          <Footer />
        </GlobalContextProvider>
        <Toaster />
      </body>
    </html>
  );
}
