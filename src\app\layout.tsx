import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/Components/Header/Header";
import ProgressLoader from "@/Components/Common/ProgressLoader";
import { Toaster } from "sonner";
import { GlobalContextProvider } from "@/utils/Context/GlobalContext";
import Footer from "@/Components/Footer/Footer";
import { ThemeProvider } from "@/Components/providers/ThemeProvider";

const interFont = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ElectronicBit Webapp",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${interFont.variable} antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <ProgressLoader />
          <GlobalContextProvider>
            <Header />
            <main className="">{children}</main>
            <Footer />
          </GlobalContextProvider>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
