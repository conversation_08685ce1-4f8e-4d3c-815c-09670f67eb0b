"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { User, Lock, Eye, EyeOff, Mail } from "lucide-react";
import useApiRequest from "@/utils/hook/useApiRequest";
import { signUpAPI, socialLoginAPI, updateMeAPI } from "@/app/action";
import { useRouter } from "nextjs-toploader/app";
import { removeCookie, setCookie } from "typescript-cookie";
import { routes, TOKEN } from "@/utils/function";
import CustomToast from "../Common/CustomToast";
import { GoogleAuthProvider } from "firebase/auth/web-extension";
import { signInWithPopup, UserCredential } from "firebase/auth";
import { auth } from "@/app/firebase/firebase-config";

// Validation schema
const validationSchema = Yup.object({
  password: Yup.string()
    .min(8, "Password must be at least 8 characters long")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
    )
    .required("Password is required"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("password")], "Oops! Passwords don’t match — try again.")
    .required("Confirm password is required"),
});

const ResetPassword: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const router = useRouter();

  const api = useApiRequest(false);

  const initialValues = {
    password: "",
    confirmPassword: "",
  };

  const handleSubmit = async (values: typeof initialValues) => {
    const payload = {
      password: values.password,
    };

    // console.log(payload);

    api.sendRequest(
      updateMeAPI,
      (res: any) => {
        removeCookie(TOKEN);

        router.push(routes.LOGIN);
      },
      { ...payload },
      "Password reset Successfully!",
      (err) => {
        // console.log(err);
        CustomToast.error(err?.message || "Something went wrong");
      }
    );
    // setIsLoading(true);
  };

  return (
    <section className="min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-white mb-2">
            Reset your Password
          </h2>
          {/* <p className="text-gray-400">Enter your new Password</p> */}
        </div>

        <div className="card">
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ isSubmitting, errors, touched }) => (
              <Form className="space-y-6">
                {/* Password Field */}
                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-300 mb-2"
                  >
                    Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <Field
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      className={`input-field pl-10 pr-10 w-full ${
                        errors.password && touched.password
                          ? "border-red-500 focus:border-red-500"
                          : ""
                      }`}
                      placeholder="Enter your password"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  <ErrorMessage
                    name="password"
                    component="div"
                    className="text-red-400 text-sm mt-1"
                  />
                </div>

                {/* Confirm Password Field */}
                <div>
                  <label
                    htmlFor="confirmPassword"
                    className="block text-sm font-medium text-gray-300 mb-2"
                  >
                    Confirm Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <Field
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      className={`input-field pl-10 pr-10 w-full ${
                        errors.confirmPassword && touched.confirmPassword
                          ? "border-red-500 focus:border-red-500"
                          : ""
                      }`}
                      placeholder="Confirm your password"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  <ErrorMessage
                    name="confirmPassword"
                    component="div"
                    className="text-red-400 text-sm mt-1"
                  />
                </div>

                {/* Resume Upload */}
                {/* <div>
              <label
                htmlFor="resume"
                className="block text-sm font-medium text-gray-300 mb-2"
              >
                Resume/CV
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FileText className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="resume"
                  name="resume"
                  type="file"
                  accept=".pdf,.doc,.docx"
                  className="input-field pl-10 w-full file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-primary-600 file:text-white hover:file:bg-primary-700"
                  onChange={handleFileChange}
                />
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Accepted formats: PDF, DOC, DOCX (Max 5MB)
              </p>
            </div> */}

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={isSubmitting || api.isLoading}
                  className="w-full btn-primary py-3 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {api.isLoading ? "Submitting..." : "Submit"}
                </button>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </section>
  );
};

export default ResetPassword;
