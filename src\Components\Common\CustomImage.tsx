import { generateAvatarCanvas } from "@/utils/function";
import Image from "next/image";
import { useEffect, useMemo, useState } from "react";

const CustomImage = ({
  src,
  alt,
  width,
  height,
  className,

  ...props
}: React.ComponentProps<typeof Image>) => {
  const [error, setError] = useState(false);

  // Memoize the generated avatar to prevent recreation on every render
  const fallbackAvatar = useMemo(() => {
    return generateAvatarCanvas(alt ?? "", 100, 0.55);
  }, [alt]);

  useEffect(() => {
    setError(false);
  }, [src]);
  return (
    <>
      <Image
        src={!error && src ? src : fallbackAvatar}
        alt={alt}
        width={width}
        height={height}
        className={className}
        onError={() => {
          setError(true);
        }}
        {...props}
      />
    </>
  );
};

export default CustomImage;
