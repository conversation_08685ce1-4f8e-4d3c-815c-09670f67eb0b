"use client"; // Required if you use hooks in Next.js App Router
import HeroSection from "./HeroSection";
import PlatformSection from "./PlatformSection";
import StatsSection from "./StatsSection";
import FeatureSection from "./FeatureSection";
import TestimonialSection from "./TestimonialSection";
import CurriculumSection from "./CurriculumSection";
import LearningJourney from "./LearningJourney";

const Home: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection />

      {/* Platform Demo Section */}
      <PlatformSection />

      {/* Features Section */}
      <FeatureSection />

      {/* Stats Section */}
      <StatsSection />

      {/* Curriculum Section */}
      <CurriculumSection />

      {/* Learning Journey */}
      <LearningJourney />

      {/* Testimonials Section */}
      <TestimonialSection />
    </div>
  );
};

export default Home;
